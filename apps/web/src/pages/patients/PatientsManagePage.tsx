import { PatientFilters } from "@/components/patients/PatientFilters";
import { PatientStats } from "@/components/patients/PatientStats";
import { PatientTable } from "@/components/patients/PatientTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataPagination } from "@/components/ui/data-pagination";
import { usePatients } from "@/hooks/patients/usePatients";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { PatientFilters as PatientFiltersType } from "@/types/patient";
import { Plus, Users } from "lucide-react";
import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

export function PatientsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [filters, setFilters] = useState<PatientFiltersType>({
    search: "",
    status: "all",
    gender: "all",
  });

  // Pagination setup
  const itemsPerPage = 50;

  // Get current page from URL parameters
  const pageParam = searchParams.get("page");
  const currentPage = Math.max(1, parseInt(pageParam || "1", 10));
  const offset = (currentPage - 1) * itemsPerPage;

  const { patients, stats, isLoading, totalCount } = usePatients({
    filters,
    limit: itemsPerPage,
    offset,
    includeStats: true,
    includeOrganization:
      isSystemAdmin && organization?.id === "system-admin-all-orgs",
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Pagination handlers
  const goToPage = (page: number) => {
    const newParams = new URLSearchParams(searchParams);
    if (page === 1) {
      newParams.delete("page");
    } else {
      newParams.set("page", page.toString());
    }
    setSearchParams(newParams);
  };

  const handleFiltersChange = (newFilters: PatientFiltersType) => {
    setFilters(newFilters);
    goToPage(1); // Reset to first page when filters change
  };

  const handleCreatePatient = () => {
    navigate("/patients/new");
  };











  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Patients";
    }
    return `${organization?.name || "Organization"} Patients`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage patients across all organizations";
    }
    return "Manage and view patient information for your organization";
  };

  return (
    <div className="h-full flex flex-col max-w-7xl mx-auto px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreatePatient}>
          <Plus className="mr-2 h-4 w-4" />
          Add Patient
        </Button>
      </div>

      {/* Stats */}
      <div className="mb-8 flex-shrink-0">
        <PatientStats stats={stats} isLoading={isLoading} />
      </div>

      {/* Main Content - Allow this to grow and handle overflow */}
      <Card className="flex-1 flex flex-col min-h-0">
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Patient Directory
            </CardTitle>
            <div className="text-sm text-muted-foreground">
              {totalCount} patient{totalCount !== 1 ? "s" : ""}
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col space-y-6 min-h-0">
          {/* Filters */}
          <div className="flex-shrink-0">
            <PatientFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              totalCount={totalCount}
            />
          </div>

          {/* Table - Allow this to scroll if needed */}
          <div className="flex-1 min-h-0 overflow-auto">
            <PatientTable patients={patients} isLoading={isLoading} />
          </div>

          {/* Pagination */}
          <div className="flex-shrink-0">
            <DataPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
