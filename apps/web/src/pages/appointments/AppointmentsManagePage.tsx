import { AppointmentFilters } from "@/components/appointments/AppointmentFilters";
import { AppointmentStats } from "@/components/appointments/AppointmentStats";
import { AppointmentTable } from "@/components/appointments/AppointmentTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataPagination } from "@/components/ui/data-pagination";
import { useAppointments } from "@/hooks/dashboard/useAppointments";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { AppointmentFilters as AppointmentFiltersType } from "@/types/appointment";
import { Calendar, Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

export function AppointmentsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [filters, setFilters] = useState<AppointmentFiltersType>({
    search: "",
    status: "all",
    dateRange: undefined,
    providerId: undefined,
    departmentId: undefined,
  });

  // Pagination setup
  const itemsPerPage = 50;

  // Get current page from URL parameters
  const pageParam = searchParams.get("page");
  const currentPage = Math.max(1, parseInt(pageParam || "1", 10));
  const offset = (currentPage - 1) * itemsPerPage;

  // Memoize the options object to prevent unnecessary re-renders
  const appointmentOptions = useMemo(() => ({
    limit: itemsPerPage,
    // Convert filters to useAppointments options
    status: filters.status !== "all" ? filters.status as any : undefined,
    dateRange: filters.dateRange,
  }), [itemsPerPage, filters.status, filters.dateRange]);

  const { appointments, isLoading, totalCount } = useAppointments(appointmentOptions);

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Pagination handlers
  const goToPage = (page: number) => {
    const newParams = new URLSearchParams(searchParams);
    if (page === 1) {
      newParams.delete("page");
    } else {
      newParams.set("page", page.toString());
    }
    setSearchParams(newParams);
  };

  const handleFiltersChange = (newFilters: AppointmentFiltersType) => {
    setFilters(newFilters);
    goToPage(1); // Reset to first page when filters change
  };

  const handleCreateAppointment = () => {
    navigate("/appointments/new");
  };

  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Appointments";
    }
    return `${organization?.name || "Organization"} Appointments`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage appointments across all organizations";
    }
    return "Manage and view appointment schedules for your organization";
  };

  return (
    <div className="space-y-8 w-full px-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreateAppointment}>
          <Plus className="mr-2 h-4 w-4" />
          Schedule Appointment
        </Button>
      </div>

      {/* Stats */}
      <AppointmentStats appointments={appointments} isLoading={isLoading} />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Appointment Schedule
            </CardTitle>
            <div className="text-sm text-muted-foreground">
              {totalCount} appointment{totalCount !== 1 ? "s" : ""}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <AppointmentFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            totalCount={totalCount}
            organization={organization}
            isSystemAdmin={isSystemAdmin}
          />

          {/* Table */}
          <AppointmentTable
            appointments={appointments}
            isLoading={isLoading}
            organization={organization}
            isSystemAdmin={isSystemAdmin}
          />

          {/* Pagination */}
          <DataPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
          />
        </CardContent>
      </Card>
    </div>
  );
}
