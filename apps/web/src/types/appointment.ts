export interface AppointmentFilters {
  search: string;
  status: "all" | "scheduled" | "checked_in" | "in_progress" | "completed" | "cancelled" | "no_show";
  dateRange?: [Date, Date];
  providerId?: string;
  departmentId?: string;
}

export interface AppointmentStats {
  total: number;
  scheduled: number;
  completed: number;
  cancelled: number;
  noShow: number;
  todayCount: number;
  upcomingCount: number;
  overdueCount: number;
}
