import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { globalRequestCache } from "@/lib/global-request-cache";
import { supabase } from "@/lib/supabase";
import { useOrganizationStore } from "@/stores/organization-store";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { Database } from "@spritely/supabase-types";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
    applyOrganizationFilter,
    useOrganizationFilter,
} from "./useOrganizationFilter";

// Define the appointment type from the appointments table
type AppointmentRecord = Database["public"]["Tables"]["appointments"]["Row"];

export interface Appointment extends AppointmentRecord {
  // Joined data
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string | null;
    phone: string | null;
  };
  department?: {
    id: string;
    name: string;
    location_id: string | null;
  };
  provider?: {
    id: string;
    first_name: string;
    last_name: string;
    provider_type: "doctor" | "nurse" | "specialist" | "admin";
  };
  organization?: {
    id: string;
    name: string;
  };
  // Computed fields
  patient_name?: string;
  department_name?: string;
  provider_name?: string;
  organization_name?: string;
  status_color?: string;
  is_today?: boolean;
  is_upcoming?: boolean;
  is_overdue?: boolean;
}

interface UseAppointmentOptions {
  limit?: number;
  status?: "scheduled" | "checked_in" | "in_progress" | "completed" | "cancelled" | "no_show";
  dateRange?: [Date, Date];
  todayOnly?: boolean;
  upcomingOnly?: boolean;
}

export function useAppointments(options: UseAppointmentOptions = {}) {
  const { organization } = useAuth();
  const { selectedLocation, shouldShowLocationSelector } = useLocations();
  const organizationFilter = useOrganizationFilter();
  const { isSystemAdmin } = useOrganizationStore();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Check if we're in system admin "All Organizations" mode
  const isSystemAdminAllOrgs = useMemo(() => {
    return isSystemAdmin && organization?.id === "system-admin-all-orgs";
  }, [isSystemAdmin, organization?.id]);

  // Determine if we should filter by location
  const shouldFilterByLocation = shouldShowLocationSelector &&
    selectedLocation &&
    selectedLocation.id !== ALL_LOCATIONS_ID;

  // Memoize the options object to prevent unnecessary re-renders
  const optionsKey = useMemo(() => {
    return JSON.stringify({
      limit: options.limit,
      status: options.status,
      dateRange: options.dateRange ? [options.dateRange[0].getTime(), options.dateRange[1].getTime()] : null,
      todayOnly: options.todayOnly,
      upcomingOnly: options.upcomingOnly,
    });
  }, [options.limit, options.status, options.dateRange, options.todayOnly, options.upcomingOnly]);

  // Memoize the location filter to prevent unnecessary re-renders
  const locationFilter = useMemo(() => ({
    shouldFilter: shouldFilterByLocation,
    locationId: selectedLocation?.id
  }), [shouldFilterByLocation, selectedLocation?.id]);

  // Memoize the fetch function to prevent unnecessary re-renders
  const fetchAppointments = useCallback(async () => {
    // Only return early if we have no organization context at all
    if (!organization) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create a cache key that includes all relevant filters and system admin status
      const cacheKey = `appointments:${isSystemAdminAllOrgs ? 'all-orgs' : organization.id}:${locationFilter.locationId || 'all'}:${optionsKey}`;

      const result = await globalRequestCache.getOrCreate(cacheKey, async () => {
        console.log(`[useAppointments] Making API request with cache key: ${cacheKey}`);

        // Start building the query with proper joins including organization
        let query = supabase.from("appointments").select(
          `
          *,
          patient:patients(id, first_name, last_name, email, phone),
          department:departments(id, name, location_id),
          provider:healthcare_providers(id, first_name, last_name, provider_type),
          organization:organizations(id, name)
        `,
          { count: "exact" },
        );

        // Only apply organization filter if we're not in "All Organizations" mode
        if (!isSystemAdminAllOrgs) {
          query = applyOrganizationFilter(query, organizationFilter);
        }

        // Apply location filter if needed
        if (locationFilter.shouldFilter && locationFilter.locationId) {
          query = query.eq('departments.location_id', locationFilter.locationId);
        }

        // Apply status filter
        if (options.status) {
          query = query.eq("status", options.status);
        }

        // Apply date range filter
        if (options.dateRange) {
          const [startDate, endDate] = options.dateRange;
          query = query
            .gte("appointment_date", startDate.toISOString())
            .lte("appointment_date", endDate.toISOString());
        }

        // Apply today only filter
        if (options.todayOnly) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);

          query = query
            .gte("appointment_date", today.toISOString())
            .lt("appointment_date", tomorrow.toISOString());
        }

        // Apply upcoming only filter
        if (options.upcomingOnly) {
          const now = new Date();
          query = query.gte("appointment_date", now.toISOString());
        }

        // Apply limit if specified
        if (options.limit) {
          query = query.limit(options.limit);
        }

        // Order by appointment date
        query = query.order("appointment_date", { ascending: true });

        const { data, error: fetchError, count } = await query;

        if (fetchError) throw new Error(fetchError.message);

        return { data, count };
      });

      // Process the data to add computed fields
      const processedAppointments = result.data.map((appointment) => {
        const appointmentDate = new Date(appointment.appointment_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Determine status color
        let statusColor = "gray";
        switch (appointment.status) {
          case "scheduled":
            statusColor = "blue";
            break;
          case "checked_in":
            statusColor = "green";
            break;
          case "in_progress":
            statusColor = "yellow";
            break;
          case "completed":
            statusColor = "emerald";
            break;
          case "cancelled":
            statusColor = "red";
            break;
          case "no_show":
            statusColor = "orange";
            break;
        }

        return {
          ...appointment,
          patient_name: appointment.patient
            ? `${appointment.patient.first_name} ${appointment.patient.last_name}`
            : "Unknown Patient",
          department_name: appointment.department?.name || "Unknown Department",
          provider_name: appointment.provider
            ? `${appointment.provider.first_name} ${appointment.provider.last_name}`
            : "Unassigned",
          organization_name: appointment.organization?.name || "Unknown Organization",
          status_color: statusColor,
          is_today: appointmentDate >= today && appointmentDate < tomorrow,
          is_upcoming: appointmentDate > new Date(),
          is_overdue: appointmentDate < new Date() && appointment.status === "scheduled",
        };
      });

      setAppointments(processedAppointments);
      if (result.count !== null) setTotalCount(result.count);
      setIsLoading(false);
    } catch (err) {
      console.error("Error fetching appointments:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch appointments"),
      );
      setIsLoading(false);
    }
  }, [organization?.id, locationFilter, optionsKey, organizationFilter, isSystemAdminAllOrgs, options]);

  // Use useEffect to call the memoized fetch function
  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  return {
    appointments,
    isLoading,
    error,
    totalCount,
    // Context information for UI
    isLocationFiltered: shouldFilterByLocation,
    currentLocation: selectedLocation,
  };
}
