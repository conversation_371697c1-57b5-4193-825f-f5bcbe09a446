import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { AppointmentFilters as AppointmentFiltersType } from "@/types/appointment";
import { Calendar, Search, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface AppointmentFiltersProps {
  filters: AppointmentFiltersType;
  onFiltersChange: (filters: AppointmentFiltersType) => void;
  totalCount?: number;
  className?: string;
  // Pass these as props to avoid redundant hook calls
  organization?: { id: string; name: string } | null;
  isSystemAdmin?: boolean;
}

export function AppointmentFilters({
  filters,
  onFiltersChange,
  totalCount = 0,
  className = "",
  organization,
  isSystemAdmin = false,
}: AppointmentFiltersProps) {
  const [providers, setProviders] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const [departments, setDepartments] = useState<
    Array<{ id: string; name: string }>
  >([]);

  // Fetch providers and departments for filter dropdowns
  useEffect(() => {
    // This would be implemented to fetch providers and departments
    // For now, we'll leave them empty
    setProviders([]);
    setDepartments([]);
  }, [organization]);

  const updateFilter = useCallback(
    (key: keyof AppointmentFiltersType, value: string | undefined | [Date, Date]) => {
      onFiltersChange({
        ...filters,
        [key]: value,
      });
    },
    [filters, onFiltersChange],
  );

  const clearFilters = useCallback(() => {
    onFiltersChange({
      search: "",
      status: "all",
      dateRange: undefined,
      providerId: undefined,
      departmentId: undefined,
    });
  }, [onFiltersChange]);

  const hasActiveFilters =
    filters.search ||
    (filters.status && filters.status !== "all") ||
    filters.dateRange ||
    filters.providerId ||
    filters.departmentId;

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Search and primary filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search appointments by patient name, provider, or reason..."
            value={filters.search}
            onChange={(e) => updateFilter("search", e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Status Filter */}
        <Select
          value={filters.status}
          onValueChange={(value) => updateFilter("status", value)}
        >
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="checked_in">Checked In</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
            <SelectItem value="no_show">No Show</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear filters button */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear
          </Button>
        )}
      </div>

      {/* Secondary filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Date Range Filter */}
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Date Range:</span>
          <Select
            value={filters.dateRange ? "custom" : "all"}
            onValueChange={(value) => {
              if (value === "all") {
                updateFilter("dateRange", undefined);
              } else if (value === "today") {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                updateFilter("dateRange", [today, tomorrow]);
              } else if (value === "week") {
                const today = new Date();
                const weekFromNow = new Date(today);
                weekFromNow.setDate(weekFromNow.getDate() + 7);
                updateFilter("dateRange", [today, weekFromNow]);
              } else if (value === "month") {
                const today = new Date();
                const monthFromNow = new Date(today);
                monthFromNow.setMonth(monthFromNow.getMonth() + 1);
                updateFilter("dateRange", [today, monthFromNow]);
              }
            }}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Dates" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Dates</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">Next 7 Days</SelectItem>
              <SelectItem value="month">Next 30 Days</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Provider Filter */}
        {providers.length > 0 && (
          <Select
            value={filters.providerId || "all"}
            onValueChange={(value) =>
              updateFilter("providerId", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Providers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Providers</SelectItem>
              {providers.map((provider) => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Department Filter */}
        {departments.length > 0 && (
          <Select
            value={filters.departmentId || "all"}
            onValueChange={(value) =>
              updateFilter("departmentId", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((department) => (
                <SelectItem key={department.id} value={department.id}>
                  {department.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Results summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div>
          {hasActiveFilters ? (
            <span>
              Showing filtered results ({totalCount} appointment
              {totalCount !== 1 ? "s" : ""})
            </span>
          ) : (
            <span>
              Showing all appointments ({totalCount} total)
            </span>
          )}
        </div>
        {filters.dateRange && (
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>
              {filters.dateRange[0].toLocaleDateString()} -{" "}
              {filters.dateRange[1].toLocaleDateString()}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
