import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useDashboardData } from "@/hooks/dashboard/useDashboardData";
import { formatDate } from "@/lib/utils";
import { Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function UpcomingAppointments() {
  const { data, isLoading, error } = useDashboardData();
  const navigate = useNavigate();

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">
            Upcoming Appointments
          </CardTitle>
          <CardDescription>Failed to load appointments</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-sm text-red-500">Error loading appointment data</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading || !data) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">
            Upcoming Appointments
          </CardTitle>
          <CardDescription>Loading appointments...</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-12 bg-muted rounded-md" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.appointments.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">
            Upcoming Appointments
          </CardTitle>
          <CardDescription>No upcoming appointments</CardDescription>
        </CardHeader>
        <CardContent className="p-4 flex items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-4">
            <Calendar className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground text-center">
              No appointments scheduled
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/appointments/new")}
            >
              Schedule Appointment
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">
            Upcoming Appointments
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-xs"
            onClick={() => navigate("/appointments")}
          >
            View All
          </Button>
        </div>
        <CardDescription>Next scheduled appointments</CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4">
          {data.appointments.map((appointment) => (
            <div
              key={appointment.id}
              className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50 cursor-pointer"
              onClick={() => navigate(`/appointments/${appointment.id}`)}
            >
              <div>
                <p className="font-medium">
                  {appointment.patient.first_name} {appointment.patient.last_name}
                </p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(appointment.appointment_date)} •{" "}
                  {appointment.provider.first_name} {appointment.provider.last_name}
                </p>
              </div>
              <Button variant="ghost" size="sm" className="h-8">
                View
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
